package com.cleevio.fundedmind.adapter.`in`.calendly

import com.cleevio.fundedmind.adapter.`in`.calendly.request.CalendlyWebhookPayload
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.module.mentoringmeeting.command.ProcessEventBookingCommand
import com.cleevio.fundedmind.application.module.mentoringmeeting.command.ProcessEventModificationCommand
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.InitiatorType
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.ModificationType
import com.cleevio.fundedmind.infrastructure.config.logger
import io.swagger.v3.oas.annotations.Hidden
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController

@Hidden
@RestController
@RequestMapping
class CalendlyWebhookController(
    private val commandBus: CommandBus,
) {
    private val logger = logger()

    @PostMapping("/webhooks/calendly")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun handleWebhook(@RequestBody event: CalendlyWebhookPayload) {
        when (event.eventType) {
            "invitee.created" -> {
                logger.info("Received 'invitee.created' event: $event")

                when (event.payload.status) {
                    // Process booking, identify trader and schedule
                    "active" -> {
                        commandBus(
                            ProcessEventBookingCommand(
                                traderUri = event.payload.scheduledEvent.eventMemberships.first().user,
                                studentEmail = event.payload.email,
                                startAt = event.payload.scheduledEvent.startTime,
                                finishAt = event.payload.scheduledEvent.endTime,
                                scheduledEventUri = event.payload.scheduledEvent.uri,
                                meetingUrl = event.payload.scheduledEvent.location.joinUrl,
                            ),
                        )
                    }

                    else -> {
                        logger.error("Unknown event status! Event: $event")
                    }
                }
            }

            "invitee.canceled" -> {
                val modificationType = if (event.payload.rescheduled) {
                    ModificationType.RESCHEDULE
                } else {
                    ModificationType.CANCELLATION
                }

                logger.info("Received 'invitee.canceled' [$modificationType] event: $event")

                val modificationData = event.payload.cancellation!!

                commandBus(
                    ProcessEventModificationCommand(
                        traderUri = event.payload.scheduledEvent.eventMemberships.first().user,
                        studentEmail = event.payload.email,
                        scheduledEventUri = event.payload.scheduledEvent.uri,
                        initiator = modificationData.cancelerType.toInitiatorType(),
                        reason = modificationData.reason,
                        type = modificationType,
                    ),
                )
            }

            else -> {
                // Handle other events as necessary
                error("Unknown event type: $event")
            }
        }
    }

    private fun String.toInitiatorType(): InitiatorType = when (this) {
        "host" -> InitiatorType.MENTOR
        "invitee" -> InitiatorType.STUDENT
        else -> error("Unknown canceler type: $this")
    }
}

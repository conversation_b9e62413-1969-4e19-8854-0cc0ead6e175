package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.common.port.out.GetUserPort
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserNotFoundException
import com.cleevio.fundedmind.application.module.user.appuser.query.GetUserQuery
import com.cleevio.fundedmind.jooq.tables.references.APP_USER
import com.cleevio.fundedmind.jooq.tables.references.STUDENT
import org.jooq.DSLContext
import org.jooq.Record
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class GetUserJooq(private val dslContext: DSLContext) : GetUserPort {

    override fun invoke(userId: UUID): GetUserQuery.Result = dslContext
        .select(
            APP_USER.ID,
            APP_USER.CREATED_AT,
            APP_USER.EMAIL,
            APP_USER.ROLE,
            STUDENT.ID,
        )
        .from(APP_USER)
        .leftJoin(STUDENT).on(APP_USER.ID.eq(STUDENT.ID))
        .where(APP_USER.ID.eq(userId))
        .fetchOne()
        ?.map {
            GetUserQuery.Result(
                userId = it[APP_USER.ID]!!,
                createdAt = it[APP_USER.CREATED_AT]!!,
                email = it[APP_USER.EMAIL]!!,
                role = it[APP_USER.ROLE]!!,
                onboardingFinished = determineOnboardingFinished(it),
            )
        }
        ?: throw UserNotFoundException("User '$userId' not found.")

    private fun determineOnboardingFinished(it: Record): Boolean {
        val studentId = it[STUDENT.ID]
        return studentId != null
    }
}

package com.cleevio.fundedmind.application.common.util

import java.nio.charset.StandardCharsets
import java.util.HexFormat
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

fun createHmacSha256Instance(secretKey: String): Mac = Mac.getInstance(HMAC_SHA256).also {
    it.init(
        SecretKeySpec(secretKey.toByteArray(StandardCharsets.UTF_8), HMAC_SHA256),
    )
}

fun Mac.generateHmac(payload: String): ByteArray = generateHmac(
    payload.toByteArray(StandardCharsets.UTF_8),
)

fun Mac.generateHmac(payload: ByteArray): ByteArray = this.doFinal(payload)

fun Mac.generateUpperCaseHexHmac(payload: String): String = HexFormat.of().withUpperCase().formatHex(
    generateHmac(payload),
)

fun Mac.generateLowerCaseHexHmac(payload: String): String = HexFormat.of().withLowerCase().formatHex(
    generateHmac(payload),
)

fun Mac.generateLowerCaseHexHmac(payload: ByteArray): String = HexFormat.of().withLowerCase().formatHex(
    generateHmac(payload),
)

fun Mac.generateBase64Hmac(payload: ByteArray): String = generateHmac(payload).toBase64()

private const val HMAC_SHA256 = "HmacSHA256"

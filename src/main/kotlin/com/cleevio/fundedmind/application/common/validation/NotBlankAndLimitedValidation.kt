package com.cleevio.fundedmind.application.common.validation

import com.cleevio.fundedmind.application.common.validation.StringInputValidation.MAX_ALLOWED_STRING_SIZE
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass

@Constraint(validatedBy = [NullOrNotBlankAndLimitedValidator::class])
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class NullOrNotBlankAndLimited(
    val message: String = "must be null or not blank and limited to size <= $MAX_ALLOWED_STRING_SIZE",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class NullOrNotBlankAndLimitedValidator : ConstraintValidator<NullOrNotBlankAndLimited, String> {
    override fun isValid(
        value: String?,
        context: ConstraintValidatorContext?,
    ): Boolean = when {
        value == null -> true
        value.isBlank() -> false
        value.length > MAX_ALLOWED_STRING_SIZE -> false
        else -> true
    }
}

@Constraint(validatedBy = [NotBlankAndLimitedValidator::class])
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class NotBlankAndLimited(
    val message: String = "must be not blank and limited to size <= $MAX_ALLOWED_STRING_SIZE",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class NotBlankAndLimitedValidator : ConstraintValidator<NotBlankAndLimited, String> {
    override fun isValid(
        value: String?,
        context: ConstraintValidatorContext?,
    ): Boolean = when {
        value == null -> false
        value.isBlank() -> false
        value.length > MAX_ALLOWED_STRING_SIZE -> false
        else -> true
    }
}

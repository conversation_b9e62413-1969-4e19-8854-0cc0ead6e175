package com.cleevio.fundedmind.application.module.coursemodule

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.coursemodule.command.UpdateCourseModuleCommand
import com.cleevio.fundedmind.application.module.coursemodule.event.CourseModuleUpdatedEvent
import com.cleevio.fundedmind.application.module.coursemodule.finder.CourseModuleFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateCourseModuleCommandHandler(
    private val courseModuleFinderService: CourseModuleFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, UpdateCourseModuleCommand> {

    override val command = UpdateCourseModuleCommand::class

    @Transactional
    @Lock(module = Locks.CourseModule.MODULE, lockName = Locks.CourseModule.UPDATE)
    override fun handle(@LockFieldParameter("courseId") command: UpdateCourseModuleCommand) {
        courseModuleFinderService
            .getById(command.courseModuleId)
            .also { it.checkRelatedToCourse(courseId = command.courseId) }
            .apply {
                update(
                    title = command.title,
                    description = command.description,
                    shortDescription = command.shortDescription,
                    rewardDescription = command.rewardDescription,
                    rewardCouponCode = command.rewardCouponCode,
                    rewardButton = command.rewardButton?.toDomainButton(),
                    comingSoon = command.comingSoon,
                )
            }
            .also {
                applicationEventPublisher.publishEvent(
                    CourseModuleUpdatedEvent(courseModuleId = command.courseModuleId),
                )
            }
    }
}

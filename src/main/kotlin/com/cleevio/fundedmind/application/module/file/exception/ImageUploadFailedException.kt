package com.cleevio.fundedmind.application.module.file.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ImageUploadFailedException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.IMAGE_UPLOAD_FAILED,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ImageDeleteFailedException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.IMAGE_DELETE_FAILED,
    message = message,
)

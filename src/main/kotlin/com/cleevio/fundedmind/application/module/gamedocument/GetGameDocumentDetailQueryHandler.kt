package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.file.finder.AppFileFinderService
import com.cleevio.fundedmind.application.module.gamedocument.finder.GameDocumentFinderService
import com.cleevio.fundedmind.application.module.gamedocument.query.GetGameDocumentDetailQuery
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class GetGameDocumentDetailQueryHandler(
    private val gameDocumentFinderService: GameDocumentFinderService,
    private val appFileFinderService: AppFileFinderService,
) : QueryHandler<GetGameDocumentDetailQuery.Result, GetGameDocumentDetailQuery> {

    override val query = GetGameDocumentDetailQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetGameDocumentDetailQuery): GetGameDocumentDetailQuery.Result {
        val gameDocument = gameDocumentFinderService.getById(query.gameDocumentId)

        return GetGameDocumentDetailQuery.Result(
            gameDocumentId = gameDocument.id,
            studentId = gameDocument.studentId,
            type = gameDocument.type,
            issuingCompany = gameDocument.issuingCompany,
            payoutAmount = gameDocument.payoutAmount,
            reachedLevel = gameDocument.reachedLevel,
            payoutDate = gameDocument.payoutDate,
            gameDocumentFile = gameDocument.gameDocumentFileId?.let { appFileFinderService.getImageById(it) },
            truthScore = gameDocument.truthScore,
            state = gameDocument.state,
            denyMessage = gameDocument.denyMessage,
            scoreMessage = gameDocument.scoreMessage,
        )
    }
}

package com.cleevio.fundedmind.application.module.location.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.application.module.location.exception.UserLocationNotFoundException
import com.cleevio.fundedmind.domain.location.UserLocation
import com.cleevio.fundedmind.domain.location.UserLocationRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class UserLocationFinderService(
    private val userLocationRepository: UserLocationRepository,
) : BaseFinderService<UserLocation>(userLocationRepository) {

    override fun errorBlock(message: String) = throw UserLocationNotFoundException(message)

    override fun getEntityType() = UserLocation::class
}
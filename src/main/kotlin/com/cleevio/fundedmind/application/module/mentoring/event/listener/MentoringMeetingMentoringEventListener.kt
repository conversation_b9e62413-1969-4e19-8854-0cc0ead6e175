package com.cleevio.fundedmind.application.module.mentoring.event.listener

import com.cleevio.fundedmind.application.module.mentoring.event.MentoringCreatedEvent
import com.cleevio.fundedmind.application.module.mentoring.event.MentoringSessionUpdatedEvent
import com.cleevio.fundedmind.application.module.mentoring.service.UpdateUserMentoringInCrmService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class MentoringMeetingMentoringEventListener(
    private val updateUserMentoringInCrmService: UpdateUserMentoringInCrmService,
) {

    @SentryTransaction(operation = "async.crm.mentoring-created")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleMentoringCreatedAfterCommitEvent(event: MentoringCreatedEvent) {
        updateUserMentoringInCrmService(event.mentoringId)
    }

    @SentryTransaction(operation = "async.crm.mentoring-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleMentoringUpdatedAfterCommitEvent(event: MentoringSessionUpdatedEvent) {
        updateUserMentoringInCrmService(event.mentoringId)
    }
}

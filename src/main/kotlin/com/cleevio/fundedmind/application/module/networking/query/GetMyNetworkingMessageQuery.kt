package com.cleevio.fundedmind.application.module.networking.query

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class GetMyNetworkingMessageQuery(
    val recipientUserId: UUID,
    val networkingMessageId: UUID,
) : Query<GetMyNetworkingMessageQuery.Result> {

    @Schema(name = "GetMyNetworkingMessageResult")
    data class Result(
        val sender: NetworkingMessageSender,
        val recipientUserId: UUID,
        val message: String,
    )

    @Schema(name = "NetworkingMessageSender")
    data class NetworkingMessageSender(
        val id: UUID,
        val userRole: User<PERSON><PERSON>,
        val firstName: String,
        val bio: String?,
        val profilePicture: ImageResult?,

        // student props
        val discordId: String?, // null if user has disabled discord subscription
        val studentGameLevel: GameLevel?, // null if user has disabled level visibility

        // trader props
        val traderLastName: String?,
        val traderBadgeColor: BadgeColor?,
        val traderPosition: String?,
    )
}

package com.cleevio.fundedmind.application.module.product

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.common.port.out.ExistsExternalProductPort
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.common.util.ifFalse
import com.cleevio.fundedmind.application.common.util.ifTrue
import com.cleevio.fundedmind.application.module.mentoring.finder.MentoringFinderService
import com.cleevio.fundedmind.application.module.product.command.UpdateProductCommand
import com.cleevio.fundedmind.application.module.product.exception.ProductAlreadyExistsException
import com.cleevio.fundedmind.application.module.product.exception.ProductCannotBeUpdatedException
import com.cleevio.fundedmind.application.module.product.exception.ProductNotFoundException
import com.cleevio.fundedmind.application.module.product.finder.ProductFinderService
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class UpdateProductCommandHandler(
    private val mentoringFinderService: MentoringFinderService,
    private val traderFinderService: TraderFinderService,
    private val productFinderService: ProductFinderService,
    private val existsExternalProduct: ExistsExternalProductPort,
) : CommandHandler<Unit, UpdateProductCommand> {

    override val command = UpdateProductCommand::class

    @Transactional
    @Lock(module = Locks.Product.MODULE, lockName = Locks.Product.UPDATE)
    override fun handle(@LockFieldParameter("productId") command: UpdateProductCommand) {
        val product = productFinderService.getById(id = command.productId)

        traderFinderService.checkExistsById(id = command.traderId)

        if (product.stripeIdentifier != command.stripeIdentifier) {
            checkStripeIdentifier(command.stripeIdentifier)
            checkProductNotYetSold(productId = command.productId)
        }

        if (product.traderId != command.traderId) {
            checkProductNotYetSold(productId = command.productId)
        }

        product.update(
            traderId = command.traderId,
            name = command.name,
            stripeIdentifier = command.stripeIdentifier,
            description = command.description,
            altDescription = command.altDescription,
            sessionsCount = command.sessionsCount,
            validity = command.validityInDays,
        )
    }

    private fun checkStripeIdentifier(stripeProductId: StripeProductId) {
        productFinderService.existsNonDeletedByStripeIdentifier(stripeIdentifier = stripeProductId).ifTrue {
            throw ProductAlreadyExistsException("Product with stripe identifier: '$stripeProductId' already exists.")
        }

        existsExternalProduct.existsByIdentifier(stripeProductId).ifFalse {
            throw ProductNotFoundException("Stripe product: '$stripeProductId' not found.")
        }
    }

    private fun checkProductNotYetSold(productId: UUID) {
        mentoringFinderService.existsByProductId(productId = productId).ifTrue {
            throw ProductCannotBeUpdatedException("Product: '$productId' has been already sold as mentoring.")
        }
    }
}

package com.cleevio.fundedmind.application.module.progress

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.lesson.finder.LessonFinderService
import com.cleevio.fundedmind.application.module.progress.command.UserRevertsFinishLessonCommand
import com.cleevio.fundedmind.application.module.progress.finder.LessonProgressFinderService
import com.cleevio.fundedmind.application.module.progress.service.RevertLessonFinishService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UserRevertsFinishLessonCommandHandler(
    private val revertLessonFinishService: RevertLessonFinishService,
    private val lessonFinderService: LessonFinderService,
    private val lessonProgressFinderService: LessonProgressFinderService,
) : CommandHandler<Unit, UserRevertsFinishLessonCommand> {

    override val command = UserRevertsFinishLessonCommand::class

    @Transactional
    @Lock(module = Locks.Progress.MODULE, lockName = Locks.Progress.UPDATE)
    override fun handle(@LockFieldParameter("userId") command: UserRevertsFinishLessonCommand) {
        lessonFinderService.checkExistsById(command.lessonId)

        val lessonProgress = lessonProgressFinderService
            .findByLessonIdAndUserId(command.lessonId, command.userId)
            ?: return // If there's no progress, there's nothing to revert

        revertLessonFinishService.revertFinish(
            userId = lessonProgress.userId,
            lessonId = lessonProgress.lessonId,
        )
    }
}

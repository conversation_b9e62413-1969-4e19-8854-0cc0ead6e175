package com.cleevio.fundedmind.application.module.user.appuser.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class UserNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.USER_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class VerificationCodeNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.VERIFICATION_CODE_NOT_FOUND,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.CONFLICT)
class VerificationCodeAlreadyExistsException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.VERIFICATION_CODE_ALREADY_EXISTS,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class VerificationCodeHasWrongStatusException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.VERIFICATION_CODE_HAS_WRONG_STATUS,
    message = message,
)

@ResponseStatus(HttpStatus.CONFLICT)
class UserWithEmailAlreadyExistsException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.USER_WITH_EMAIL_ALREADY_EXISTS,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
class FailedToCreateUserInFirebaseException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.FAILED_TO_CREATE_USER_IN_FIREBASE,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class UserHasWrongRoleException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.USER_HAS_WRONG_ROLE,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class UserAccountHasWrongActiveStatusException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.USER_HAS_WRONG_ACTIVE_STATUS,
    message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class UserIsNotTrackedInPaymentSystemException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.USER_IS_NOT_TRACKED_IN_PAYMENT_SYSTEM,
    message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class UserHubspotIdentifierNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.USER_HUBSPOT_IDENTIFIER_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class UserHasDisabledNetworkingException(message: String) : FundedmindApiException(
    message = message,
    reason = ExtendedErrorReasonType.USER_HAS_DISABLED_NETWORKING,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class UserIsAdminException(message: String) : FundedmindApiException(
    message = message,
    reason = ExtendedErrorReasonType.USER_IS_ADMIN,
)

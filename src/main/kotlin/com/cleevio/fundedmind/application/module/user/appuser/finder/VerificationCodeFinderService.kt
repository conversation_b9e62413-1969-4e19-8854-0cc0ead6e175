package com.cleevio.fundedmind.application.module.user.appuser.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.application.module.user.appuser.exception.VerificationCodeNotFoundException
import com.cleevio.fundedmind.domain.user.appuser.VerificationCode
import com.cleevio.fundedmind.domain.user.appuser.VerificationCodeRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class VerificationCodeFinderService(
    private val verificationCodeRepository: VerificationCodeRepository,
) : BaseFinderService<VerificationCode>(verificationCodeRepository) {

    override fun errorBlock(message: String) = throw VerificationCodeNotFoundException(message)

    override fun getEntityType() = VerificationCode::class

    fun getByCodeAndAppUserId(
        code: String,
        appUserId: UUID,
    ): VerificationCode = verificationCodeRepository.findByCodeAndAppUserId(code, appUserId)
        ?: errorBlock("Verification code: '$code' not found for user: '$appUserId'.")

    fun existsByCodeAndAppUserId(
        code: String,
        appUserId: UUID,
    ): Boolean = verificationCodeRepository.existsByCodeAndAppUserId(code, appUserId)

    fun findAllByAppUserId(appUserId: UUID): List<VerificationCode> =
        verificationCodeRepository.findAllByAppUserId(appUserId)
}

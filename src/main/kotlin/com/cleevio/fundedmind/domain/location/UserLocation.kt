package com.cleevio.fundedmind.domain.location

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.DomainEntity
import com.cleevio.fundedmind.domain.common.Geometry
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.locationtech.jts.geom.Point
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Table(name = "user_location")
@Entity
@DynamicUpdate
class UserLocation private constructor(
    id: UUID,
    val street: String?,
    val city: String?,
    val postalCode: String?,
    val state: String?,
    val latitude: Double,
    val longitude: Double,
    private var location: Point,
    val obfuscatedLatitude: Double,
    val obfuscatedLongitude: Double,
    private var obfuscatedLocation: Point,
) : DomainEntity(id) {

    companion object {
        fun newLocation(
            id: UUID = UUIDv7.randomUUID(),
            street: String?,
            city: String?,
            postalCode: String?,
            state: String?,
            latitude: Double,
            longitude: Double,
            obfuscatedLatitude: Double,
            obfuscatedLongitude: Double,
        ) = UserLocation(
            id = id,
            street = street,
            city = city,
            postalCode = postalCode,
            state = state,
            latitude = latitude,
            longitude = longitude,
            location = Geometry.createPoint(longitude, latitude),
            obfuscatedLatitude = obfuscatedLatitude,
            obfuscatedLongitude = obfuscatedLongitude,
            obfuscatedLocation = Geometry.createPoint(obfuscatedLongitude, obfuscatedLatitude),
        )
    }

    /**
     * Examples of output:
     * - All fields: `"Main Street 123, 017 01 Prague, Czech Republic"`
     * - Missing street: `"017 01 Prague, Czech Republic"`
     * - Missing postal code: `"Main Street 123, Prague, Czech Republic"`
     * - Missing city: `"Main Street 123, 017 01, Czech Republic"`
     * - Only street: `"Main Street 123"`
     * - Only state: `"Czech Republic"`
     * - All null: `"N/A"`
     */
    val formattedAddress: String
        get() = run {
            if (street == null && postalCode == null && city == null && state == null) {
                return "N/A"
            }

            val parts = mutableListOf<String>()

            // Add street if present
            street?.let { parts.add(it) }

            // Add postal code and city together if both present, or individually
            when {
                postalCode != null && city != null -> parts.add("$postalCode $city")
                postalCode != null -> parts.add(postalCode)
                city != null -> parts.add(city)
            }

            // Add state if present
            state?.let { parts.add(it) }

            return parts.joinToString(", ")
        }
}

@Repository
interface UserLocationRepository : JpaRepository<UserLocation, UUID>
package com.cleevio.fundedmind.infrastructure.config

import io.sentry.Sentry
import io.sentry.spring.jakarta.SentryTaskDecorator
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
import org.springframework.boot.autoconfigure.condition.ConditionalOnThreading
import org.springframework.boot.autoconfigure.thread.Threading
import org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder
import org.springframework.boot.task.SimpleAsyncTaskSchedulerCustomizer
import org.springframework.boot.task.ThreadPoolTaskSchedulerCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.concurrent.SimpleAsyncTaskScheduler
import javax.sql.DataSource

/**
 * `@EnableSchedulerLock(defaultLockAtMostFor = "1h")` is just a safety net
 * in case that the node executing the task dies and the lock is not released.
 *
 * It should ideally be set on each scheduled task to significantly larger number
 * than the maximum estimated execution time.
 *
 * [ShedLock GitHub](https://github.com/lukas-krecan/ShedLock?tab=readme-ov-file#behavior)
 */
@Configuration
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "1h")
@Profile("!test")
class SchedulingConfig {

    private val logger = logger()

    /**
     * This is not needed unless you are using @EnableWebSocketMessageBroker
     * Because AbstractMessageBrokerConfiguration TaskScheduler bean kept overriding default Spring TaskScheduler
     */
    @Bean
    @ConditionalOnThreading(Threading.VIRTUAL)
    fun taskScheduler(builder: SimpleAsyncTaskSchedulerBuilder): SimpleAsyncTaskScheduler = builder.build()

    @Bean
    @ConditionalOnThreading(Threading.VIRTUAL)
    fun schedulerVirtual() = SimpleAsyncTaskSchedulerCustomizer {
        it.setTaskDecorator(SentryTaskDecorator())
        it.setErrorHandler { ex ->
            Sentry.captureException(ex)
            logger.error("Exception in scheduled task: ", ex)
        }
        it.setThreadNamePrefix("scheduler-")
    }

    @Bean
    @ConditionalOnThreading(Threading.PLATFORM)
    fun schedulerPlatform() = ThreadPoolTaskSchedulerCustomizer {
        it.setTaskDecorator(SentryTaskDecorator())
        it.setErrorHandler { ex ->
            Sentry.captureException(ex)
            logger.error("Exception in scheduled task: ", ex)
        }
        it.setThreadNamePrefix("scheduler-")
    }

    @Bean
    fun lockProvider(dataSource: DataSource): LockProvider {
        val lockProviderConfig = JdbcTemplateLockProvider.Configuration
            .builder()
            .withTableName("_shedlock")
            .withJdbcTemplate(JdbcTemplate(dataSource))
            .usingDbTime()
            .build()

        return JdbcTemplateLockProvider(lockProviderConfig)
    }
}

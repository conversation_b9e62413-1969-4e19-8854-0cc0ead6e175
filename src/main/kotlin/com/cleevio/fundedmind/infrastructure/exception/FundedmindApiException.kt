package com.cleevio.fundedmind.infrastructure.exception

import com.cleevio.library.exceptionhandler.service.model.ApiException
import com.cleevio.library.exceptionhandler.service.model.ErrorCode
import com.cleevio.library.exceptionhandler.service.model.ErrorReason
import com.cleevio.library.exceptionhandler.service.model.Source

abstract class FundedmindApiException(
    reason: ErrorReason,
    message: String,
    shouldBeLoggedToSentry: Boolean = true,
) : ApiException(
    source = ErrorSource,
    reason = reason,
    message = message,
    shouldBeLogged = shouldBeLoggedToSentry,
)

private object ErrorSource : Source {
    override val errorCode: ErrorCode
        get() = ErrorCode("FUNDED-MIND")
}

package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.GeoLocationInput
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ListUserLocationsJooqTest(
    @Autowired private val underTest: ListUserLocationsJooq,
) : IntegrationTest() {

    @Test
    fun `should return empty list when no locations in bounding box`() {
        // given
        val northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0)
        val southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0)

        // when
        val result = underTest.invoke(northEast, southWest)

        // then
        result.size shouldBe 0
    }

    @Test
    fun `should return locations within bounding box`() {
        // given
        val inBoxLocation = dataHelper.getUserLocation(
            latitude = 49.5,
            longitude = 14.5,
            obfuscatedLatitude = 49.5,
            obfuscatedLongitude = 14.5,
        )

        val outOfBoxLocation = dataHelper.getUserLocation(
            latitude = 50.5,
            longitude = 15.5,
            obfuscatedLatitude = 50.5,
            obfuscatedLongitude = 15.5,
        )

        // Location outside the bounding box
        dataHelper.getUserLocation(
            latitude = 51.0,
            longitude = 16.0,
            obfuscatedLatitude = 51.0,
            obfuscatedLongitude = 16.0,
        )

        val northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0)
        val southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0)

        // when
        val result = underTest.invoke(northEast, southWest)

        // then
        result shouldHaveSize 1
        result[0].run {
            userLocationId shouldBe inBoxLocation.id
            obfuscatedLocation.latitude shouldBe 49.5
            obfuscatedLocation.longitude shouldBe 14.5
        }
    }

    @Test
    fun `should return multiple locations within bounding box`() {
        // given
        val location1 = dataHelper.getUserLocation(
            latitude = 49.2,
            longitude = 14.2,
            obfuscatedLatitude = 49.2,
            obfuscatedLongitude = 14.2,
        )

        val location2 = dataHelper.getUserLocation(
            latitude = 49.8,
            longitude = 14.8,
            obfuscatedLatitude = 49.8,
            obfuscatedLongitude = 14.8,
        )

        // Location outside the bounding box
        dataHelper.getUserLocation(
            latitude = 51.0,
            longitude = 16.0,
            obfuscatedLatitude = 51.0,
            obfuscatedLongitude = 16.0,
        )

        val northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0)
        val southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0)

        // when
        val result = underTest.invoke(northEast, southWest)

        // then
        result shouldHaveSize 2

        // Verify both locations are in the result
        result.any { it.userLocationId == location1.id } shouldBe true
        result.any { it.userLocationId == location2.id } shouldBe true

        // Verify the properties of the locations
        val resultLocation1 = result.first { it.userLocationId == location1.id }
        resultLocation1.obfuscatedLocation.latitude shouldBe 49.2
        resultLocation1.obfuscatedLocation.longitude shouldBe 14.2

        val resultLocation2 = result.first { it.userLocationId == location2.id }
        resultLocation2.obfuscatedLocation.latitude shouldBe 49.8
        resultLocation2.obfuscatedLocation.longitude shouldBe 14.8
    }
}

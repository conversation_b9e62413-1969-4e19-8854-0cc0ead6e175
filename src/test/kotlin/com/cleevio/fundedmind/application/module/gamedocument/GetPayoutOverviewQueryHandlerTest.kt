package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.query.GetPayoutOverviewQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate
import java.time.Year

class GetPayoutOverviewQueryHandlerTest(
    @Autowired private val underTest: GetPayoutOverviewQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return payout overview with approved payouts and offset`() {
        val year = Year.of(2025)

        // Create students
        val student1 = dataHelper.getAppUser(id = 201.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }
        val student2 = dataHelper.getAppUser(id = 202.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.EXCLUSIVE)
        }

        // Create approved payout documents for the year
        val approvedPayout1 = dataHelper.getGameDocument(
            studentId = student1.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("1000.00"),
            payoutDate = LocalDate.of(2025, 6, 15),
            entityModifier = { it.approve() },
        )

        val approvedPayout2 = dataHelper.getGameDocument(
            studentId = student2.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("500.50"),
            payoutDate = LocalDate.of(2025, 8, 20),
            entityModifier = { it.approve() },
        )

        // Create a waiting payout (should not be counted)
        val waitingPayout = dataHelper.getGameDocument(
            studentId = student1.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("300.00"),
            payoutDate = LocalDate.of(2025, 9, 10),
        ) // state remains WAITING

        // Create a certificate (should not be counted)
        dataHelper.getGameDocument(
            studentId = student2.id,
            type = GameDocumentType.CERTIFICATE,
            payoutAmount = null,
            payoutDate = LocalDate.of(2025, 7, 5),
            entityModifier = { it.approve() },
        )

        // Create payout offset for the year
        dataHelper.getGamePayoutOffset(
            year = year,
            payoutOffset = BigDecimal("250.75"),
        )

        val result = underTest.handle(GetPayoutOverviewQuery(year = year))

        result.run {
            approvedPayouts shouldBe 2
            realTotalPayout shouldBeEqualComparingTo BigDecimal("1500.50") // 1000.00 + 500.50
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("1751.25") // 1500.50 + 250.75
        }
    }

    @Test
    fun `should return zero values when no approved payouts exist`() {
        val year = Year.of(2024)

        // Create a student
        val student = dataHelper.getAppUser(id = 203.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP)
        }

        // Create only waiting/denied payouts
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("100.00"),
            payoutDate = LocalDate.of(2024, 5, 10),
        ) // state remains WAITING

        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("200.00"),
            payoutDate = LocalDate.of(2024, 6, 15),
            entityModifier = { it.deny("Rejected") },
        )

        // Create payout offset for the year
        dataHelper.getGamePayoutOffset(
            year = year,
            payoutOffset = BigDecimal("100.00"),
        )

        val result = underTest.handle(GetPayoutOverviewQuery(year = year))

        result.run {
            approvedPayouts shouldBe 0
            realTotalPayout shouldBeEqualComparingTo BigDecimal.ZERO
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("100.00") // 0 + 100.00
        }
    }

    @Test
    fun `should return zero offset when no offset exists for year`() {
        val year = Year.of(2023)

        // Create a student
        val student = dataHelper.getAppUser(id = 204.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create approved payout
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("750.25"),
            payoutDate = LocalDate.of(2023, 12, 1),
            entityModifier = { it.approve() },
        )

        // No offset created for this year

        val result = underTest.handle(GetPayoutOverviewQuery(year = year))

        result.run {
            approvedPayouts shouldBe 1
            realTotalPayout shouldBeEqualComparingTo BigDecimal("750.25")
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("750.25") // 750.25 + 0
        }
    }

    @Test
    fun `should filter payouts by year correctly`() {
        val targetYear = Year.of(2025)
        val otherYear = Year.of(2024)

        // Create a student
        val student = dataHelper.getAppUser(id = 205.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.EXCLUSIVE)
        }

        // Create approved payout for target year
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("400.00"),
            payoutDate = LocalDate.of(2025, 3, 15),
            entityModifier = { it.approve() },
        )

        // Create approved payout for different year (should not be counted)
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("600.00"),
            payoutDate = LocalDate.of(2024, 11, 20),
            entityModifier = { it.approve() },
        )

        // Create offset for target year
        dataHelper.getGamePayoutOffset(
            year = targetYear,
            payoutOffset = BigDecimal("50.00"),
        )

        val result = underTest.handle(GetPayoutOverviewQuery(year = targetYear))

        result.run {
            approvedPayouts shouldBe 1
            realTotalPayout shouldBeEqualComparingTo BigDecimal("400.00")
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("450.00") // 400.00 + 50.00
        }
    }

    @Test
    fun `should use current year as default when no year specified`() {
        val currentYear = Year.now()

        // Create a student
        val student = dataHelper.getAppUser(id = 206.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Create approved payout for current year
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("123.45"),
            payoutDate = LocalDate.now(),
            entityModifier = { it.approve() },
        )

        // Create offset for current year
        dataHelper.getGamePayoutOffset(
            year = currentYear,
            payoutOffset = BigDecimal("10.55"),
        )

        val result = underTest.handle(GetPayoutOverviewQuery()) // No year specified, should use current year

        result.run {
            approvedPayouts shouldBe 1
            realTotalPayout shouldBeEqualComparingTo BigDecimal("123.45")
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("134.00") // 123.45 + 10.55
        }
    }

    @Test
    fun `should handle negative offset correctly`() {
        val year = Year.of(2025)

        // Create a student
        val student = dataHelper.getAppUser(id = 207.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP)
        }

        // Create approved payout
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("1000.00"),
            payoutDate = LocalDate.of(2025, 4, 10),
            entityModifier = { it.approve() },
        )

        // Create negative offset
        dataHelper.getGamePayoutOffset(
            year = year,
            payoutOffset = BigDecimal("-150.50"),
        )

        val result = underTest.handle(GetPayoutOverviewQuery(year = year))

        result.run {
            approvedPayouts shouldBe 1
            realTotalPayout shouldBeEqualComparingTo BigDecimal("1000.00")
            offsetTotalPayout shouldBeEqualComparingTo BigDecimal("849.50") // 1000.00 + (-150.50)
        }
    }
}
